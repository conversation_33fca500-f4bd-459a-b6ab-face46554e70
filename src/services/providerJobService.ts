import { ApiResponse, apiService } from './api';

// Provider Job Booking Status Types
export type JobBookingStatus = 'pending' | 'active' | 'completed';

// Provider Job Booking Interfaces
export interface JobBookingService {
  category: string;
  tasks: string[];
  customTask?: string | null;
}

export interface JobBookingProperty {
  type: string;
}

export interface JobBookingSchedule {
  date: string;
  timePreference: string;
  frequency: string;
  recurringFrequency?: string | null;
}

export interface JobBookingLocation {
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface JobBookingContact {
  fullName: string;
  email: string;
  phone: string;
}

export interface JobBookingUser {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

export interface JobBookingAsset {
  uuid: string;
  file_name: string;
  url: string;
  mime_type: string;
  file_size: number;
}

export interface ProviderJobBooking {
  id: string;
  jobId: string;
  projectCode: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  jobType: string;
  property: JobBookingProperty;
  service: JobBookingService;
  description?: string | null;
  schedule: JobBookingSchedule;
  budget?: string | number | null;
  location: JobBookingLocation;
  contact: JobBookingContact;
  assets: JobBookingAsset[];
  user?: JobBookingUser | null;
  completedAt?: string | null;
}

export interface ProviderJobBookingListResponse {
  data: ProviderJobBooking[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface ProviderJobBookingDetailsResponse {
  success: boolean;
  data: ProviderJobBooking;
  message?: string;
}

// Job Status Update Interfaces
export interface JobStatusUpdateRequest {
  status: string;
}

export interface JobStatusUpdateResponse {
  success: boolean;
  message?: string;
  data?: {
    id: string;
    status: string;
    updatedAt: string;
  };
}

export interface JobActionResponse {
  success: boolean;
  message?: string;
  data?: {
    id: string;
    status: string;
    startedAt?: string;
    completedAt?: string;
    updatedAt: string;
  };
}

/**
 * Fetch provider job bookings with optional status filtering
 * @param status Optional status filter ('pending', 'active', 'completed')
 * @param page Page number for pagination (default: 1)
 * @param perPage Items per page (default: 10)
 * @param token Optional authentication token
 * @returns Promise with paginated job bookings list
 */
export const fetchProviderJobBookings = async (
  status?: JobBookingStatus,
  page: number = 1,
  perPage: number = 10,
  token?: string
): Promise<ApiResponse<ProviderJobBookingListResponse>> => {
  // Build query parameters
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
  });

  // Add status filter if provided
  if (status) {
    params.append('status', status);
  }

  // Set up headers
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  // Make API call
  return apiService<ProviderJobBookingListResponse>(`/api/provider/jobs?${params}`, {
    method: 'GET',
    requiresAuth: true,
    headers,
  });
};

/**
 * Fetch details for a specific provider job booking
 * @param jobId The job booking ID
 * @param token Optional authentication token
 * @returns Promise with job booking details
 */
export const fetchProviderJobBookingDetails = async (
  jobId: string,
  token?: string
): Promise<ApiResponse<ProviderJobBookingDetailsResponse>> => {
  // Set up headers
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  // Make API call
  return apiService<ProviderJobBookingDetailsResponse>(`/api/provider/jobs/${jobId}`, {
    method: 'GET',
    requiresAuth: true,
    headers,
  });
};

/**
 * Update the status of a provider job booking
 * @param jobId The job booking ID
 * @param status The new status to set
 * @param token Optional authentication token
 * @returns Promise with status update response
 */
export const updateProviderJobBookingStatus = async (
  jobId: string,
  status: string,
  token?: string
): Promise<ApiResponse<JobStatusUpdateResponse>> => {
  // Set up headers
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  // Prepare request payload
  const payload: JobStatusUpdateRequest = { status };

  // Make API call
  return apiService<JobStatusUpdateResponse>(`/api/provider/jobs/${jobId}/status`, {
    method: 'PATCH',
    body: payload,
    requiresAuth: true,
    headers,
  });
};

/**
 * Mark a provider job booking as started
 * @param jobId The job booking ID
 * @param token Optional authentication token
 * @returns Promise with job start response
 */
export const markJobAsStarted = async (
  jobId: string,
  token?: string
): Promise<ApiResponse<JobActionResponse>> => {
  // Set up headers
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  // Make API call
  return apiService<JobActionResponse>(`/api/provider/jobs/${jobId}/start`, {
    method: 'POST',
    requiresAuth: true,
    headers,
  });
};

/**
 * Mark a provider job booking as completed
 * @param jobId The job booking ID
 * @param token Optional authentication token
 * @returns Promise with job completion response
 */
export const markJobAsCompleted = async (
  jobId: string,
  token?: string
): Promise<ApiResponse<JobActionResponse>> => {
  // Set up headers
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  // Make API call
  return apiService<JobActionResponse>(`/api/provider/jobs/${jobId}/complete`, {
    method: 'POST',
    requiresAuth: true,
    headers,
  });
};

// Export service object for consistency with other services
export const providerJobService = {
  fetchProviderJobBookings,
  fetchProviderJobBookingDetails,
  updateProviderJobBookingStatus,
  markJobAsStarted,
  markJobAsCompleted,
};
