import { ApiResponse, apiService } from './api';
import { Bid, BidListResponse, ProviderBid, BidsSummary } from '../types/bid';

// Provider-specific bid interfaces
export interface ProviderJobBookingWithBids {
  id: string;
  jobId: string;
  projectCode: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  jobType: string;
  description?: string | null;
  budget?: string | number | null;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  contact: {
    fullName: string;
    email: string;
    phone: string;
  };
  user?: {
    id: number;
    name: string;
    email: string;
    avatar?: string;
  } | null;
  // Provider's bid information for this job
  provider_bid?: ProviderBid | null;
  // Summary of all bids for this job
  bids_summary?: BidsSummary;
  // All bids for this job (if provider has access)
  bids?: Bid[];
}

export interface ProviderJobBookingWithBidsResponse {
  success: boolean;
  data: ProviderJobBookingWithBids;
  message?: string;
}

export interface ProviderBidsListResponse {
  data: Bid[];
  meta?: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

/**
 * Fetch bids for a specific job booking from provider's perspective
 * This endpoint retrieves job booking details along with associated bids
 * @param jobId The job booking ID
 * @param token Optional authentication token
 * @returns Promise with job booking details and bids
 */
export const fetchBidsForJob = async (
  jobId: string,
  token?: string
): Promise<ApiResponse<ProviderJobBookingWithBidsResponse>> => {
  try {
    // Validate jobId
    if (!jobId || typeof jobId !== 'string') {
      return {
        data: null,
        error: 'Valid job ID is required',
        status: 400,
        isSuccess: false
      };
    }

    // Set up headers
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Make API call using the provider-scoped endpoint
    const response = await apiService<ProviderJobBookingWithBidsResponse>(
      `/api/provider/job-bookings/${jobId}`,
      {
        method: 'GET',
        requiresAuth: true,
        headers,
      }
    );

    return response;
  } catch (error) {
    console.error('Error fetching bids for job:', error);
    return {
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch bids for job',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Fetch all bids made by the current provider
 * @param page Page number for pagination (default: 1)
 * @param perPage Items per page (default: 10)
 * @param token Optional authentication token
 * @returns Promise with provider's bids list
 */
export const fetchMyBids = async (
  page: number = 1,
  perPage: number = 10,
  token?: string
): Promise<ApiResponse<ProviderBidsListResponse>> => {
  try {
    // Build query parameters
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString(),
    });

    // Set up headers
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Make API call using the provider-scoped endpoint
    const response = await apiService<ProviderBidsListResponse>(
      `/api/provider/bids?${params}`,
      {
        method: 'GET',
        requiresAuth: true,
        headers,
      }
    );

    return response;
  } catch (error) {
    console.error('Error fetching provider bids:', error);
    return {
      data: null,
      error: error instanceof Error ? error.message : 'Failed to fetch provider bids',
      status: 500,
      isSuccess: false
    };
  }
};

// Export service object for consistency with other services
export const providerBidService = {
  fetchBidsForJob,
  fetchMyBids,
};
